import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LinkPrecedence } from '@prisma/client';

@Injectable()
export class ContactsService {
  constructor(private prisma: PrismaService) {}

  async createContact(phoneNumber?: string, email?: string) {
    // Basic contact creation logic
    const contact = await this.prisma.contact.create({
      data: {
        phoneNumber,
        email,
        linkPrecedence: LinkPrecedence.primary,
      },
    });

    return {
      success: true,
      data: contact,
      message: 'Contact created successfully',
    };
  }

  async getAllContacts() {
    const contacts = await this.prisma.contact.findMany({
      where: {
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      success: true,
      data: contacts,
      message: 'Contacts retrieved successfully',
    };
  }

  async getContactById(id: number) {
    const contact = await this.prisma.contact.findUnique({
      where: { id },
      include: {
        primaryContact: true,
        linkedContacts: true,
      },
    });

    if (!contact) {
      throw new Error('Contact not found');
    }

    return {
      success: true,
      data: contact,
      message: 'Contact retrieved successfully',
    };
  }

  // Identify contact for Bitespeed identity reconciliation
  async identifyContact(identifyDto: { email?: string; phoneNumber?: string }) {
    // TODO: Implement the core logic for identity reconciliation
    return { contact: {} };
  }
}
