import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ContactsService } from './contacts.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('contacts')
@Controller('contacts')
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new contact' })
  @ApiResponse({ status: 201, description: 'Contact created successfully' })
  async createContact(
    @Body() createContactDto: { phoneNumber?: string; email?: string },
  ) {
    return this.contactsService.createContact(
      createContactDto.phoneNumber,
      createContactDto.email,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all contacts' })
  @ApiResponse({ status: 200, description: 'Contacts retrieved successfully' })
  async getAllContacts() {
    return this.contactsService.getAllContacts();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get contact by ID' })
  @ApiResponse({ status: 200, description: 'Contact retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Contact not found' })
  async getContactById(@Param('id', ParseIntPipe) id: number) {
    return this.contactsService.getContactById(id);
  }

  @Post('identify')
  async identify(
    @Body() identifyDto: { email?: string; phoneNumber?: string },
  ) {
    return this.contactsService.identifyContact(identifyDto);
  }
}
