// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Contact {
  id              Int      @id @default(autoincrement())
  phoneNumber     String?  @db.VarChar(15)
  email           String?  @db.VarChar(255)
  linkedId        Int?     // Reference to primary contact
  linkPrecedence  LinkPrecedence
  createdAt       DateTime @default(now()) @db.Timestamp(3)
  updatedAt       DateTime @updatedAt @db.Timestamp(3)
  deletedAt       DateTime? @db.Timestamp(3)

  // Self-referential relationship for linked contacts
  primaryContact  Contact? @relation("ContactLinks", fields: [linkedId], references: [id])
  linkedContacts  Contact[] @relation("ContactLinks")

  @@map("contacts")
  @@index([phoneNumber])
  @@index([email])
  @@index([linkedId])
  @@index([linkPrecedence])
}

enum LinkPrecedence {
  primary
  secondary
}
